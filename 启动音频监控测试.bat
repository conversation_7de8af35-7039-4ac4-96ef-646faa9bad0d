@echo off
chcp 65001 >nul
title 本地音频监控测试

echo.
echo 🎵 本地音频监控测试工具
echo ========================
echo.

REM 检查虚拟环境是否存在
if not exist ".venv\Scripts\python.exe" (
    echo ❌ 虚拟环境不存在，请先创建虚拟环境
    echo.
    echo 创建虚拟环境命令：
    echo python -m venv .venv
    echo.
    pause
    exit /b 1
)

echo ✅ 找到虚拟环境
echo.

REM 检查依赖是否已安装
echo 🔍 检查依赖...
.venv\Scripts\python.exe -c "import pyaudio, numpy; print('✅ 依赖检查通过')" 2>nul
if errorlevel 1 (
    echo ❌ 依赖未安装，正在自动安装...
    echo.
    .venv\Scripts\python.exe install_audio_deps.py
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo.
echo 🚀 启动本地音频监控测试...
echo.
.venv\Scripts\python.exe test_local_audio.py

echo.
echo 测试程序已退出
pause
