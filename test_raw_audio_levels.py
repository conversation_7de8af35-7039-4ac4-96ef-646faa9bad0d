#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原始音频电平测试脚本
演示不转换为dB的原始音频数值监控
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QDoubleSpinBox, QProgressBar, QTextEdit, QRadioButton, QButtonGroup
from PyQt5.QtCore import Qt, QTimer, pyqtSlot

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import pyaudio
        import numpy
        from local_audio_monitor import LocalAudioMonitor, AudioIntegrationManager
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False

class RawAudioTestWidget(QWidget):
    """原始音频电平测试界面"""
    
    def __init__(self):
        super().__init__()
        
        if not check_dependencies():
            sys.exit(1)
        
        from local_audio_monitor import LocalAudioMonitor, AudioIntegrationManager
        
        # 创建两个监控器：一个用dB，一个用原始数值
        self.monitor_db = LocalAudioMonitor(use_db=True)
        self.monitor_raw = LocalAudioMonitor(use_db=False)
        self.integration_db = AudioIntegrationManager(self)
        self.integration_raw = AudioIntegrationManager(self)
        
        # 设置集成管理器的监控器
        self.integration_db.local_monitor = self.monitor_db
        self.integration_raw.local_monitor = self.monitor_raw
        
        self.init_ui()
        self.setup_connections()
        
        # 音频电平数据
        self.audio_levels_db = {}
        self.audio_levels_raw = {}
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(50)  # 50ms更新一次，更流畅
        
        # 当前使用的模式
        self.current_mode = "db"  # "db" 或 "raw"
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("原始音频电平 vs dB对比测试")
        self.setGeometry(200, 200, 900, 700)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("🎵 原始音频电平 vs dB 对比测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2563eb;
                padding: 15px;
                background: #f0f9ff;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # 说明文本
        info_text = QLabel("""
        这个测试对比两种音频电平表示方式：
        
        📊 dB模式：传统的分贝表示，范围通常是-60dB到0dB
        📈 原始数值模式：直接使用音频信号强度，范围是0.0到1.0
        
        原始数值的优势：
        • 计算更快，无需对数运算
        • 数值更直观，0.0=静音，1.0=最大音量
        • 适合简单的阈值判断
        """)
        info_text.setStyleSheet("""
            QLabel {
                color: #374151;
                background: #f9fafb;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #3b82f6;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info_text)
        
        # 设备选择
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("音频设备:"))
        
        self.device_combo = QComboBox()
        self.device_combo.setMinimumWidth(300)
        device_layout.addWidget(self.device_combo)
        
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.refresh_devices)
        device_layout.addWidget(refresh_btn)
        
        layout.addLayout(device_layout)
        
        # 模式选择
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("监控模式:"))
        
        self.mode_group = QButtonGroup()
        self.db_radio = QRadioButton("dB模式 (-60dB ~ 0dB)")
        self.raw_radio = QRadioButton("原始数值模式 (0.0 ~ 1.0)")
        self.db_radio.setChecked(True)
        
        self.mode_group.addButton(self.db_radio, 0)
        self.mode_group.addButton(self.raw_radio, 1)
        self.mode_group.buttonClicked.connect(self.on_mode_changed)
        
        mode_layout.addWidget(self.db_radio)
        mode_layout.addWidget(self.raw_radio)
        mode_layout.addStretch()
        layout.addLayout(mode_layout)
        
        # 阈值设置
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("触发阈值:"))
        
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(-60.0, 0.0)
        self.threshold_spin.setValue(-30.0)
        self.threshold_spin.setSuffix(" dB")
        self.threshold_spin.setDecimals(1)
        threshold_layout.addWidget(self.threshold_spin)
        
        threshold_layout.addStretch()
        layout.addLayout(threshold_layout)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("▶️ 开始监控")
        self.start_btn.clicked.connect(self.start_monitoring)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止监控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 对比显示区域
        compare_layout = QHBoxLayout()
        
        # dB显示
        db_group = QWidget()
        db_layout = QVBoxLayout(db_group)
        db_title = QLabel("📊 dB模式")
        db_title.setAlignment(Qt.AlignCenter)
        db_title.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #dc2626;
                padding: 8px;
                background: #fef2f2;
                border-radius: 6px;
            }
        """)
        db_layout.addWidget(db_title)
        
        self.db_progress = QProgressBar()
        self.db_progress.setRange(-60, 0)
        self.db_progress.setValue(-60)
        self.db_progress.setFormat("%v dB")
        db_layout.addWidget(self.db_progress)
        
        self.db_label = QLabel("电平: -60.0 dB")
        self.db_label.setAlignment(Qt.AlignCenter)
        self.db_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                color: #374151;
                padding: 8px;
                background: #f9fafb;
                border-radius: 6px;
            }
        """)
        db_layout.addWidget(self.db_label)
        
        self.db_status = QLabel("状态: 静音")
        self.db_status.setAlignment(Qt.AlignCenter)
        self.db_status.setStyleSheet("""
            QLabel {
                font-size: 11pt;
                color: #6b7280;
                padding: 6px;
                background: #f3f4f6;
                border-radius: 4px;
            }
        """)
        db_layout.addWidget(self.db_status)
        
        compare_layout.addWidget(db_group)
        
        # 原始数值显示
        raw_group = QWidget()
        raw_layout = QVBoxLayout(raw_group)
        raw_title = QLabel("📈 原始数值模式")
        raw_title.setAlignment(Qt.AlignCenter)
        raw_title.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #059669;
                padding: 8px;
                background: #ecfdf5;
                border-radius: 6px;
            }
        """)
        raw_layout.addWidget(raw_title)
        
        self.raw_progress = QProgressBar()
        self.raw_progress.setRange(0, 1000)  # 0.0-1.0 * 1000
        self.raw_progress.setValue(0)
        self.raw_progress.setFormat("%.3f")
        raw_layout.addWidget(self.raw_progress)
        
        self.raw_label = QLabel("电平: 0.000")
        self.raw_label.setAlignment(Qt.AlignCenter)
        self.raw_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                color: #374151;
                padding: 8px;
                background: #f9fafb;
                border-radius: 6px;
            }
        """)
        raw_layout.addWidget(self.raw_label)
        
        self.raw_status = QLabel("状态: 静音")
        self.raw_status.setAlignment(Qt.AlignCenter)
        self.raw_status.setStyleSheet("""
            QLabel {
                font-size: 11pt;
                color: #6b7280;
                padding: 6px;
                background: #f3f4f6;
                border-radius: 4px;
            }
        """)
        raw_layout.addWidget(self.raw_status)
        
        compare_layout.addWidget(raw_group)
        
        layout.addLayout(compare_layout)
        
        # 日志区域
        log_title = QLabel("📝 实时日志")
        log_title.setStyleSheet("font-weight: bold; font-size: 14pt; color: #1f2937; margin-top: 10px;")
        layout.addWidget(log_title)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background: #1f2937;
                color: #f9fafb;
                border: 1px solid #374151;
                border-radius: 6px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
            }
        """)
        layout.addWidget(self.log_text)
        
        self.setLayout(layout)
        
        # 初始化设备列表
        self.refresh_devices()
    
    def setup_connections(self):
        """设置信号连接"""
        self.monitor_db.audio_level_changed.connect(self.on_db_level_changed)
        self.monitor_raw.audio_level_changed.connect(self.on_raw_level_changed)
        self.integration_db.speaking_state_changed.connect(self.on_db_speaking_changed)
        self.integration_raw.speaking_state_changed.connect(self.on_raw_speaking_changed)
    
    def refresh_devices(self):
        """刷新设备列表"""
        self.device_combo.clear()
        devices = self.monitor_db.get_audio_devices()
        
        for device in devices:
            display_name = f"{device['name']} ({device['channels']}ch)"
            self.device_combo.addItem(display_name, device['index'])
        
        if len(devices) == 0:
            self.device_combo.addItem("没有找到音频设备", -1)
    
    def on_mode_changed(self, button):
        """模式切换"""
        if button == self.db_radio:
            self.current_mode = "db"
            self.threshold_spin.setRange(-60.0, 0.0)
            self.threshold_spin.setValue(-30.0)
            self.threshold_spin.setSuffix(" dB")
            self.threshold_spin.setDecimals(1)
        else:
            self.current_mode = "raw"
            self.threshold_spin.setRange(0.0, 1.0)
            self.threshold_spin.setValue(0.01)
            self.threshold_spin.setSuffix("")
            self.threshold_spin.setDecimals(3)
    
    def start_monitoring(self):
        """开始监控"""
        if self.device_combo.count() == 0 or self.device_combo.currentData() == -1:
            return
        
        device_index = self.device_combo.currentData()
        threshold = self.threshold_spin.value()
        
        # 同时启动两种模式的监控
        self.integration_db.add_source_mapping("test_db", "测试源", device_index, -30.0, 0.5)
        self.integration_raw.add_source_mapping("test_raw", "测试源", device_index, 0.01, 0.5)
        
        self.integration_db.start_monitoring_all()
        self.integration_raw.start_monitoring_all()
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.device_combo.setEnabled(False)
        
        self.log(f"✅ 开始双模式监控，阈值: {threshold}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.integration_db.stop_monitoring_all()
        self.integration_raw.stop_monitoring_all()
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.device_combo.setEnabled(True)
        
        # 重置显示
        self.db_progress.setValue(-60)
        self.db_label.setText("电平: -60.0 dB")
        self.db_status.setText("状态: 静音")
        
        self.raw_progress.setValue(0)
        self.raw_label.setText("电平: 0.000")
        self.raw_status.setText("状态: 静音")
        
        self.log("⏹️ 停止监控")
    
    @pyqtSlot(str, float)
    def on_db_level_changed(self, source_name, db_level):
        """dB电平变化"""
        self.audio_levels_db[source_name] = db_level
    
    @pyqtSlot(str, float)
    def on_raw_level_changed(self, source_name, raw_level):
        """原始电平变化"""
        self.audio_levels_raw[source_name] = raw_level
    
    @pyqtSlot(str, bool)
    def on_db_speaking_changed(self, source_name, is_speaking):
        """dB模式说话状态变化"""
        if is_speaking:
            self.db_status.setText("状态: 🎤 检测到声音")
            self.db_status.setStyleSheet("""
                QLabel {
                    font-size: 11pt;
                    color: #dc2626;
                    padding: 6px;
                    background: #fef2f2;
                    border-radius: 4px;
                }
            """)
        else:
            self.db_status.setText("状态: 静音")
            self.db_status.setStyleSheet("""
                QLabel {
                    font-size: 11pt;
                    color: #6b7280;
                    padding: 6px;
                    background: #f3f4f6;
                    border-radius: 4px;
                }
            """)
    
    @pyqtSlot(str, bool)
    def on_raw_speaking_changed(self, source_name, is_speaking):
        """原始模式说话状态变化"""
        if is_speaking:
            self.raw_status.setText("状态: 🎤 检测到声音")
            self.raw_status.setStyleSheet("""
                QLabel {
                    font-size: 11pt;
                    color: #dc2626;
                    padding: 6px;
                    background: #fef2f2;
                    border-radius: 4px;
                }
            """)
        else:
            self.raw_status.setText("状态: 静音")
            self.raw_status.setStyleSheet("""
                QLabel {
                    font-size: 11pt;
                    color: #6b7280;
                    padding: 6px;
                    background: #f3f4f6;
                    border-radius: 4px;
                }
            """)
    
    def update_display(self):
        """更新显示"""
        # 更新dB显示
        if "test_db" in self.audio_levels_db:
            db_level = self.audio_levels_db["test_db"]
            self.db_progress.setValue(int(db_level))
            self.db_label.setText(f"电平: {db_level:.1f} dB")
        
        # 更新原始数值显示
        if "test_raw" in self.audio_levels_raw:
            raw_level = self.audio_levels_raw["test_raw"]
            self.raw_progress.setValue(int(raw_level * 1000))
            self.raw_label.setText(f"电平: {raw_level:.3f}")
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def closeEvent(self, event):
        """关闭事件"""
        self.integration_db.cleanup()
        self.integration_raw.cleanup()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
        }
        QComboBox, QDoubleSpinBox, QPushButton {
            padding: 6px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            background: white;
        }
        QComboBox:focus, QDoubleSpinBox:focus {
            border-color: #3b82f6;
        }
        QPushButton {
            background: #3b82f6;
            color: white;
            font-weight: bold;
        }
        QPushButton:hover {
            background: #2563eb;
        }
        QPushButton:disabled {
            background: #9ca3af;
            color: #6b7280;
        }
        QProgressBar {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #10b981, stop:0.7 #f59e0b, stop:1 #dc2626);
            border-radius: 6px;
        }
    """)
    
    # 创建测试窗口
    window = RawAudioTestWidget()
    window.show()
    
    print("🎵 原始音频电平对比测试启动")
    print("可以同时看到dB和原始数值两种表示方式")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
