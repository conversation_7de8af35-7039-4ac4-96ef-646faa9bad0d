# 🎵 本地音频监控功能集成说明

## 📖 功能概述

本次更新为OBS去重软件的三重音频闪避功能添加了**本地音频监控**支持，可以直接监听本地电脑的音频设备，实现更精确、更快速的音频闪避控制。

## ✨ 新增功能

### 1. 本地音频监控模块 (`local_audio_monitor.py`)
- **LocalAudioMonitor**: 核心音频监控类，使用PyAudio直接访问音频设备
- **AudioIntegrationManager**: 音频集成管理器，连接本地监控和OBS控制
- 支持多设备同时监控
- 实时音频电平检测和dB转换
- 可配置的触发阈值和保持时间

### 2. 依赖安装脚本 (`install_audio_deps.py`)
- 自动检测操作系统并安装相应依赖
- 支持Windows、Linux、macOS
- 智能错误处理和故障排除提示
- 安装完成后自动测试功能

### 3. 测试工具
- **test_local_audio.py**: 完整的音频监控测试界面
- **audio_monitoring_example.py**: 简化的使用示例
- 实时音频电平显示和状态监控

## 🚀 安装步骤

### 1. 安装依赖
```bash
# 使用虚拟环境启动（推荐）
E:\OBS\.venv\Scripts\python.exe install_audio_deps.py

# 或者直接运行
python install_audio_deps.py
```

### 2. 测试功能
```bash
# 测试本地音频监控
E:\OBS\.venv\Scripts\python.exe test_local_audio.py

# 运行使用示例
E:\OBS\.venv\Scripts\python.exe audio_monitoring_example.py
```

### 3. 启动主程序
```bash
# 使用虚拟环境启动主程序
E:\OBS\.venv\Scripts\python.exe main_module.py
```

## 🎛️ 使用方法

### 在主程序中使用

1. **启动程序**：使用虚拟环境启动主程序
2. **连接OBS**：确保OBS Studio正在运行并连接成功
3. **进入音频去重页面**：选择"🎵 音频去重"标签页
4. **选择三重音频闪避**：选择"🎯 三重音频闪避"子标签页

### 配置本地音频监控

1. **启用本地监控**：
   - 勾选"使用本地音频监控（推荐）"选项
   - 如果PyAudio未安装，会显示安装提示

2. **配置音频源**：
   - 为每个音频源（真人声音、AI声音、碎片化声音）选择对应的OBS音频源
   - 如果启用了本地监控，还需要选择对应的本地音频设备

3. **设置参数**：
   - **触发阈值**：音频超过此值时触发闪避（推荐-30dB到-20dB）
   - **闪避音量**：其他音频源被压低到的音量（推荐0.03）
   - **保持时间**：停止说话后继续保持闪避的时间（推荐0.5秒）

4. **启动闪避**：
   - 点击"启用三重音频闪避系统"
   - 系统会自动选择最佳的监控方式

## 🔧 技术原理

### 监控模式对比

| 特性 | OBS WebSocket监控 | 本地音频监控 |
|------|------------------|-------------|
| **响应速度** | 较慢（100ms轮询） | 快速（实时） |
| **精确度** | 依赖OBS音量设置 | 直接检测音频电平 |
| **CPU占用** | 低 | 中等 |
| **依赖** | 仅需OBS连接 | 需要PyAudio |
| **稳定性** | 高 | 高 |

### 优先级系统

音频闪避遵循严格的优先级规则：
- **真人声音**（优先级0）：最高优先级，可以压制所有其他声音
- **AI声音**（优先级1）：中等优先级，可以压制碎片化声音
- **碎片化声音**（优先级2）：最低优先级，会被其他声音压制

### 音频电平检测

```python
# 音频电平转换为dB
if rms > 0:
    db_level = 20 * np.log10(rms / 32767.0)  # 16位音频
else:
    db_level = -60.0  # 静音
```

## 📊 配置建议

### 不同环境的阈值设置

| 环境类型 | 推荐阈值 | 说明 |
|---------|---------|------|
| **安静环境** | -35dB 到 -30dB | 家庭录音室、深夜录制 |
| **一般环境** | -30dB 到 -25dB | 普通房间、白天录制 |
| **嘈杂环境** | -25dB 到 -20dB | 办公室、有背景噪音 |

### 保持时间设置

| 用途 | 推荐时间 | 说明 |
|------|---------|------|
| **快速对话** | 0.3-0.5秒 | 适合快节奏对话 |
| **正常对话** | 0.5-1.0秒 | 一般录制场景 |
| **慢速讲解** | 1.0-2.0秒 | 教学、演讲类内容 |

## 🐛 故障排除

### 常见问题

#### Q: 提示"PyAudio未安装"？
**A**: 运行依赖安装脚本：
```bash
python install_audio_deps.py
```

#### Q: 无法检测到音频设备？
**A**: 
1. 确保音频设备已正确连接并被系统识别
2. 检查设备驱动是否正常
3. 在系统音频设置中测试设备
4. 重启程序或重新插拔设备

#### Q: 音频检测不准确？
**A**:
1. 调整触发阈值（降低阈值提高灵敏度）
2. 检查环境噪音水平
3. 确认音频设备增益设置
4. 测试不同的采样率设置

#### Q: 闪避反应太慢？
**A**:
1. 启用"使用本地音频监控"选项
2. 降低音频缓冲区大小
3. 检查系统性能和CPU使用率
4. 关闭不必要的后台程序

#### Q: 程序崩溃或卡死？
**A**:
1. 检查音频设备是否被其他程序占用
2. 确认所有依赖库版本兼容
3. 以管理员权限运行程序
4. 查看控制台错误信息

### 性能优化建议

1. **降低采样率**：如果不需要高质量音频分析，可以降低采样率
2. **增大缓冲区**：减少CPU使用，但会增加延迟
3. **减少监控源**：只监控必要的音频源
4. **优化阈值**：避免频繁的状态切换

## 📈 使用场景

### 场景1：直播三重闪避
配置三个音频源：
1. **真人麦克风**（优先级0）- 主播说话时压制所有其他声音
2. **AI语音输出**（优先级1）- AI回复时压制背景音乐
3. **背景音乐**（优先级2）- 被其他声音压制

### 场景2：录制音频去重
监控多个音频输入：
1. **主麦克风** - 主要录音源
2. **备用麦克风** - 备用录音源  
3. **系统音频** - 电脑声音

### 场景3：会议音频管理
自动管理会议音频：
1. **主讲人麦克风** - 最高优先级
2. **参与者麦克风** - 中等优先级
3. **背景音乐** - 最低优先级

## 🔄 更新日志

### v1.3.1 (2025-01-29)
- ✨ 新增本地音频监控功能
- 🎯 集成到三重音频闪避系统
- 🛠️ 添加依赖安装脚本
- 🧪 提供完整的测试工具
- 📚 完善文档和使用说明

## 📞 技术支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 运行测试程序检查功能：`python test_local_audio.py`
3. 检查系统音频设备状态
4. 查看控制台输出的错误信息
5. 联系技术支持获取帮助

---

**注意**：本功能需要访问音频设备权限，首次运行时可能需要授权。建议使用虚拟环境来避免依赖冲突。
