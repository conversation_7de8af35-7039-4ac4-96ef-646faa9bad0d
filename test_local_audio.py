#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地音频监控测试脚本
用于测试本地音频监控功能是否正常工作
"""

import sys
import time
import signal
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QSpinBox, QProgressBar, QTextEdit
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from local_audio_monitor import LocalAudioMonitor, AudioIntegrationManager

class AudioTestWidget(QWidget):
    """音频测试界面"""
    
    def __init__(self):
        super().__init__()
        self.monitor = LocalAudioMonitor()
        self.init_ui()
        self.setup_connections()
        
        # 定时器用于更新界面
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(100)  # 100ms更新一次
        
        # 音频电平数据
        self.audio_levels = {}
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("本地音频监控测试")
        self.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("🎵 本地音频监控测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2563eb;
                padding: 10px;
                background: #f0f9ff;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # PyAudio状态
        self.status_label = QLabel()
        self.update_status_label()
        layout.addWidget(self.status_label)
        
        if not self.monitor.pyaudio_available:
            # 如果PyAudio不可用，显示安装提示
            install_label = QLabel("""
            ❌ PyAudio未安装或不可用
            
            请运行以下命令安装依赖：
            python install_audio_deps.py
            
            或手动安装：
            pip install pyaudio numpy
            """)
            install_label.setStyleSheet("""
                QLabel {
                    color: #dc2626;
                    background: #fef2f2;
                    padding: 15px;
                    border-radius: 8px;
                    border-left: 4px solid #dc2626;
                }
            """)
            layout.addWidget(install_label)
            self.setLayout(layout)
            return
        
        # 设备选择区域
        device_group = QWidget()
        device_layout = QVBoxLayout(device_group)
        
        device_title = QLabel("🎤 音频设备选择")
        device_title.setStyleSheet("font-weight: bold; font-size: 14pt; color: #1f2937;")
        device_layout.addWidget(device_title)
        
        # 设备下拉框
        device_select_layout = QHBoxLayout()
        device_select_layout.addWidget(QLabel("选择设备:"))
        
        self.device_combo = QComboBox()
        self.device_combo.setMinimumWidth(300)
        device_select_layout.addWidget(self.device_combo)
        
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.refresh_devices)
        device_select_layout.addWidget(refresh_btn)
        
        device_select_layout.addStretch()
        device_layout.addLayout(device_select_layout)
        
        # 阈值设置
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("触发阈值:"))
        
        self.threshold_spin = QSpinBox()
        self.threshold_spin.setRange(-60, 0)
        self.threshold_spin.setValue(-30)
        self.threshold_spin.setSuffix(" dB")
        threshold_layout.addWidget(self.threshold_spin)
        
        threshold_layout.addStretch()
        device_layout.addLayout(threshold_layout)
        
        layout.addWidget(device_group)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("▶️ 开始监控")
        self.start_btn.clicked.connect(self.start_monitoring)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #059669;
            }
        """)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止监控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background: #dc2626;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #b91c1c;
            }
            QPushButton:disabled {
                background: #9ca3af;
            }
        """)
        control_layout.addWidget(self.stop_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 音频电平显示
        level_group = QWidget()
        level_layout = QVBoxLayout(level_group)
        
        level_title = QLabel("📊 音频电平监控")
        level_title.setStyleSheet("font-weight: bold; font-size: 14pt; color: #1f2937;")
        level_layout.addWidget(level_title)
        
        # 电平条
        self.level_bar = QProgressBar()
        self.level_bar.setRange(-60, 0)
        self.level_bar.setValue(-60)
        self.level_bar.setFormat("%v dB")
        self.level_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #10b981, stop:0.7 #f59e0b, stop:1 #dc2626);
                border-radius: 6px;
            }
        """)
        level_layout.addWidget(self.level_bar)
        
        # 数值显示
        self.level_label = QLabel("当前电平: -60.0 dB")
        self.level_label.setAlignment(Qt.AlignCenter)
        self.level_label.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #374151;
                padding: 10px;
                background: #f9fafb;
                border-radius: 6px;
            }
        """)
        level_layout.addWidget(self.level_label)
        
        # 说话状态
        self.speaking_label = QLabel("状态: 静音")
        self.speaking_label.setAlignment(Qt.AlignCenter)
        self.speaking_label.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #6b7280;
                padding: 8px;
                background: #f3f4f6;
                border-radius: 6px;
            }
        """)
        level_layout.addWidget(self.speaking_label)
        
        layout.addWidget(level_group)
        
        # 日志区域
        log_group = QWidget()
        log_layout = QVBoxLayout(log_group)
        
        log_title = QLabel("📝 监控日志")
        log_title.setStyleSheet("font-weight: bold; font-size: 14pt; color: #1f2937;")
        log_layout.addWidget(log_title)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background: #1f2937;
                color: #f9fafb;
                border: 1px solid #374151;
                border-radius: 6px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        self.setLayout(layout)
        
        # 初始化设备列表
        self.refresh_devices()
    
    def update_status_label(self):
        """更新状态标签"""
        if self.monitor.pyaudio_available:
            self.status_label.setText("✅ PyAudio可用")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #059669;
                    background: #ecfdf5;
                    padding: 8px;
                    border-radius: 6px;
                    font-weight: bold;
                }
            """)
        else:
            self.status_label.setText("❌ PyAudio不可用")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #dc2626;
                    background: #fef2f2;
                    padding: 8px;
                    border-radius: 6px;
                    font-weight: bold;
                }
            """)
    
    def setup_connections(self):
        """设置信号连接"""
        if self.monitor.pyaudio_available:
            self.monitor.audio_level_changed.connect(self.on_audio_level_changed)
            self.monitor.device_error.connect(self.on_device_error)
    
    def refresh_devices(self):
        """刷新设备列表"""
        if not self.monitor.pyaudio_available:
            return
        
        self.device_combo.clear()
        devices = self.monitor.get_audio_devices()
        
        for device in devices:
            display_name = f"{device['name']} ({device['channels']}ch, {device['sample_rate']}Hz)"
            self.device_combo.addItem(display_name, device['index'])
        
        self.log(f"刷新设备列表，找到 {len(devices)} 个输入设备")
    
    def start_monitoring(self):
        """开始监控"""
        if self.device_combo.count() == 0:
            self.log("❌ 没有可用的音频设备")
            return
        
        device_index = self.device_combo.currentData()
        threshold_db = self.threshold_spin.value()
        
        if self.monitor.start_monitoring("测试设备", device_index, threshold_db):
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.device_combo.setEnabled(False)
            self.threshold_spin.setEnabled(False)
            self.log(f"✅ 开始监控设备，阈值: {threshold_db}dB")
        else:
            self.log("❌ 启动监控失败")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitor.stop_monitoring("测试设备")
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.device_combo.setEnabled(True)
        self.threshold_spin.setEnabled(True)
        
        # 重置显示
        self.level_bar.setValue(-60)
        self.level_label.setText("当前电平: -60.0 dB")
        self.speaking_label.setText("状态: 静音")
        self.speaking_label.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #6b7280;
                padding: 8px;
                background: #f3f4f6;
                border-radius: 6px;
            }
        """)
        
        self.log("⏹️ 停止监控")
    
    @pyqtSlot(str, float)
    def on_audio_level_changed(self, source_name, db_level):
        """音频电平变化"""
        self.audio_levels[source_name] = db_level
    
    @pyqtSlot(str, str)
    def on_device_error(self, source_name, error_msg):
        """设备错误"""
        self.log(f"❌ 设备错误: {error_msg}")
    
    def update_display(self):
        """更新显示"""
        if "测试设备" in self.audio_levels:
            db_level = self.audio_levels["测试设备"]
            threshold_db = self.threshold_spin.value()
            
            # 更新电平条
            self.level_bar.setValue(int(db_level))
            
            # 更新数值显示
            self.level_label.setText(f"当前电平: {db_level:.1f} dB")
            
            # 更新说话状态
            is_speaking = db_level > threshold_db
            if is_speaking:
                self.speaking_label.setText("状态: 🎤 检测到声音")
                self.speaking_label.setStyleSheet("""
                    QLabel {
                        font-size: 14pt;
                        font-weight: bold;
                        color: #dc2626;
                        padding: 8px;
                        background: #fef2f2;
                        border-radius: 6px;
                    }
                """)
            else:
                self.speaking_label.setText("状态: 静音")
                self.speaking_label.setStyleSheet("""
                    QLabel {
                        font-size: 14pt;
                        font-weight: bold;
                        color: #6b7280;
                        padding: 8px;
                        background: #f3f4f6;
                        border-radius: 6px;
                    }
                """)
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def closeEvent(self, event):
        """关闭事件"""
        self.monitor.cleanup()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
        }
        QComboBox, QSpinBox {
            padding: 6px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            background: white;
        }
        QComboBox:focus, QSpinBox:focus {
            border-color: #3b82f6;
        }
    """)
    
    # 创建测试窗口
    window = AudioTestWidget()
    window.show()
    
    # 处理Ctrl+C
    def signal_handler(sig, frame):
        print("\n正在退出...")
        window.monitor.cleanup()
        app.quit()
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
