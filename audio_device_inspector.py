#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频设备检查工具
详细显示系统中所有可用的音频设备信息
"""

import sys
import json

def check_dependencies():
    """检查依赖"""
    try:
        import pyaudio
        return True, pyaudio
    except ImportError:
        print("❌ PyAudio未安装，请运行: python install_audio_deps.py")
        return False, None

def get_device_type_description(device_info):
    """根据设备信息判断设备类型"""
    name = device_info['name'].lower()
    
    # 物理麦克风
    if any(keyword in name for keyword in ['microphone', 'mic', '麦克风', 'webcam', 'camera']):
        return "🎤 物理麦克风"
    
    # 线路输入
    elif any(keyword in name for keyword in ['line in', 'aux', 'input']):
        return "🔌 线路输入"
    
    # 虚拟音频设备
    elif any(keyword in name for keyword in ['vb-cable', 'voicemeeter', 'virtual', 'vac']):
        return "🔄 虚拟音频设备"
    
    # 系统音频捕获
    elif any(keyword in name for keyword in ['stereo mix', 'what u hear', 'wave out mix', '立体声混音']):
        return "🔊 系统音频捕获"
    
    # 扬声器/耳机（输出设备）
    elif any(keyword in name for keyword in ['speakers', 'headphones', 'earphones', '扬声器', '耳机']):
        return "🔊 音频输出设备"
    
    # USB设备
    elif 'usb' in name:
        return "🔌 USB音频设备"
    
    # 蓝牙设备
    elif any(keyword in name for keyword in ['bluetooth', 'bt', '蓝牙']):
        return "📶 蓝牙音频设备"
    
    # HDMI音频
    elif 'hdmi' in name:
        return "📺 HDMI音频"
    
    # 默认分类
    else:
        return "❓ 其他音频设备"

def inspect_audio_devices():
    """检查所有音频设备"""
    success, pyaudio = check_dependencies()
    if not success:
        return
    
    print("🎵 音频设备详细检查")
    print("=" * 80)
    
    try:
        pa = pyaudio.PyAudio()
        device_count = pa.get_device_count()
        
        print(f"📊 检测到 {device_count} 个音频设备\n")
        
        input_devices = []
        output_devices = []
        
        for i in range(device_count):
            try:
                device_info = pa.get_device_info_by_index(i)
                
                # 设备基本信息
                device_data = {
                    'index': i,
                    'name': device_info['name'],
                    'type': get_device_type_description(device_info),
                    'max_input_channels': device_info['maxInputChannels'],
                    'max_output_channels': device_info['maxOutputChannels'],
                    'default_sample_rate': int(device_info['defaultSampleRate']),
                    'host_api': device_info['hostApi']
                }
                
                # 分类设备
                if device_info['maxInputChannels'] > 0:
                    input_devices.append(device_data)
                if device_info['maxOutputChannels'] > 0:
                    output_devices.append(device_data)
                    
            except Exception as e:
                print(f"⚠️ 获取设备 {i} 信息失败: {e}")
        
        # 显示输入设备（可以监听的）
        print("🎤 可监听的输入设备:")
        print("-" * 80)
        if input_devices:
            for device in input_devices:
                print(f"[{device['index']:2d}] {device['type']}")
                print(f"     名称: {device['name']}")
                print(f"     输入通道: {device['max_input_channels']}")
                print(f"     采样率: {device['default_sample_rate']} Hz")
                print(f"     Host API: {device['host_api']}")
                print()
        else:
            print("❌ 没有找到可用的输入设备")
        
        print("\n" + "=" * 80)
        print("🔊 系统输出设备（参考）:")
        print("-" * 80)
        if output_devices:
            for device in output_devices:
                if device['max_input_channels'] == 0:  # 只显示纯输出设备
                    print(f"[{device['index']:2d}] {device['type']}")
                    print(f"     名称: {device['name']}")
                    print(f"     输出通道: {device['max_output_channels']}")
                    print(f"     采样率: {device['default_sample_rate']} Hz")
                    print()
        
        print("\n" + "=" * 80)
        print("💡 设备类型说明:")
        print("-" * 80)
        print("🎤 物理麦克风      - 真实的麦克风设备，可以捕获环境声音")
        print("🔌 线路输入        - 音频接口的输入端口")
        print("🔄 虚拟音频设备    - 软件创建的虚拟设备（如VB-Cable）")
        print("🔊 系统音频捕获    - 可以捕获系统播放的所有声音")
        print("📶 蓝牙音频设备    - 蓝牙耳机/音箱")
        print("🔌 USB音频设备     - USB接口的音频设备")
        print("📺 HDMI音频       - 通过HDMI输出的音频")
        
        print("\n" + "=" * 80)
        print("🎯 监听本地输出声道的方法:")
        print("-" * 80)
        print("1. 🔊 启用'立体声混音'（Stereo Mix）")
        print("   - 右键系统音量图标 → 声音 → 录制")
        print("   - 右键空白处 → 显示已禁用的设备")
        print("   - 启用'立体声混音'或'What U Hear'")
        print()
        print("2. 🔄 使用虚拟音频设备")
        print("   - 安装VB-Cable或Voicemeeter")
        print("   - 将系统音频输出到虚拟设备")
        print("   - 监听虚拟设备的输入端")
        print()
        print("3. 🎛️ 使用专业音频接口")
        print("   - 音频接口通常有监听功能")
        print("   - 可以直接监听输出信号")
        
        # 检查是否有系统音频捕获设备
        stereo_mix_found = False
        for device in input_devices:
            if any(keyword in device['name'].lower() for keyword in ['stereo mix', 'what u hear', 'wave out mix', '立体声混音']):
                stereo_mix_found = True
                break
        
        print("\n" + "=" * 80)
        if stereo_mix_found:
            print("✅ 检测到系统音频捕获设备，可以直接监听本地输出声道！")
        else:
            print("⚠️ 未检测到系统音频捕获设备")
            print("   建议启用'立体声混音'或安装虚拟音频设备")
        
        pa.terminate()
        
    except Exception as e:
        print(f"❌ 检查音频设备时出错: {e}")

def main():
    """主函数"""
    inspect_audio_devices()
    
    print("\n" + "=" * 80)
    print("🔧 如需测试特定设备，请运行:")
    print("   python test_local_audio.py")
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
