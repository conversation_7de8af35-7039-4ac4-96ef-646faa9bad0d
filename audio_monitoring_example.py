#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地音频监控使用示例
演示如何在OBS去重软件中使用本地音频监控功能
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QSpinBox, QMessageBox
from PyQt5.QtCore import Qt, QTimer, pyqtSlot

def check_dependencies():
    """检查依赖是否已安装"""
    missing_deps = []
    
    try:
        import pyaudio
        print("✅ PyAudio 可用")
    except ImportError:
        missing_deps.append("pyaudio")
    
    try:
        import numpy
        print("✅ NumPy 可用")
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        from local_audio_monitor import LocalAudioMonitor, AudioIntegrationManager
        print("✅ 本地音频监控模块可用")
    except ImportError:
        missing_deps.append("local_audio_monitor")
    
    if missing_deps:
        print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
        print("\n请运行以下命令安装依赖:")
        print("python install_audio_deps.py")
        return False
    
    return True

class AudioMonitoringDemo(QWidget):
    """音频监控演示界面"""
    
    def __init__(self):
        super().__init__()
        
        if not check_dependencies():
            QMessageBox.critical(
                None,
                "依赖缺失",
                "缺少必要的依赖库。\n\n"
                "请运行 install_audio_deps.py 安装依赖，\n"
                "或手动安装：pip install pyaudio numpy"
            )
            sys.exit(1)
        
        from local_audio_monitor import LocalAudioMonitor, AudioIntegrationManager
        
        self.monitor = LocalAudioMonitor()
        self.integration = AudioIntegrationManager(self)
        
        self.init_ui()
        self.setup_connections()
        
        # 音频电平数据
        self.audio_levels = {}
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(100)
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("本地音频监控演示")
        self.setGeometry(300, 300, 600, 400)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("🎵 本地音频监控演示")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2563eb;
                padding: 15px;
                background: #f0f9ff;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # 说明文本
        info_text = QLabel("""
        这个演示展示了如何使用本地音频监控功能：
        
        1. 选择要监控的音频设备
        2. 设置触发阈值（dB）
        3. 开始监控，程序会实时检测音频电平
        4. 当音频超过阈值时，会触发相应的事件
        
        在实际的OBS去重软件中，这些事件会用来控制音频闪避。
        """)
        info_text.setStyleSheet("""
            QLabel {
                color: #374151;
                background: #f9fafb;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #3b82f6;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info_text)
        
        # 设备选择
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("音频设备:"))
        
        self.device_combo = QComboBox()
        self.device_combo.setMinimumWidth(300)
        device_layout.addWidget(self.device_combo)
        
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.refresh_devices)
        device_layout.addWidget(refresh_btn)
        
        layout.addLayout(device_layout)
        
        # 阈值设置
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("触发阈值:"))
        
        self.threshold_spin = QSpinBox()
        self.threshold_spin.setRange(-60, 0)
        self.threshold_spin.setValue(-30)
        self.threshold_spin.setSuffix(" dB")
        threshold_layout.addWidget(self.threshold_spin)
        
        threshold_layout.addStretch()
        layout.addLayout(threshold_layout)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("▶️ 开始监控")
        self.start_btn.clicked.connect(self.start_monitoring)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止监控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        layout.addLayout(control_layout)
        
        # 状态显示
        self.status_label = QLabel("状态: 未开始")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #6b7280;
                padding: 10px;
                background: #f3f4f6;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(self.status_label)
        
        # 音频电平显示
        self.level_label = QLabel("音频电平: -60.0 dB")
        self.level_label.setAlignment(Qt.AlignCenter)
        self.level_label.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #374151;
                padding: 10px;
                background: #f9fafb;
                border-radius: 8px;
            }
        """)
        layout.addWidget(self.level_label)
        
        self.setLayout(layout)
        
        # 初始化设备列表
        self.refresh_devices()
    
    def setup_connections(self):
        """设置信号连接"""
        self.monitor.audio_level_changed.connect(self.on_audio_level_changed)
        self.monitor.device_error.connect(self.on_device_error)
        self.integration.speaking_state_changed.connect(self.on_speaking_state_changed)
    
    def refresh_devices(self):
        """刷新设备列表"""
        self.device_combo.clear()
        devices = self.monitor.get_audio_devices()
        
        for device in devices:
            display_name = f"{device['name']} ({device['channels']}ch)"
            self.device_combo.addItem(display_name, device['index'])
        
        if len(devices) == 0:
            self.device_combo.addItem("没有找到音频设备", -1)
    
    def start_monitoring(self):
        """开始监控"""
        if self.device_combo.count() == 0 or self.device_combo.currentData() == -1:
            QMessageBox.warning(self, "警告", "没有可用的音频设备")
            return
        
        device_index = self.device_combo.currentData()
        threshold_db = self.threshold_spin.value()
        
        # 添加音频源映射
        self.integration.add_source_mapping(
            local_name="demo_source",
            obs_name="演示音频源",
            device_index=device_index,
            threshold_db=threshold_db,
            hold_time=0.5
        )
        
        # 开始监控
        if self.integration.start_monitoring_all():
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.device_combo.setEnabled(False)
            self.threshold_spin.setEnabled(False)
            
            self.status_label.setText("状态: 监控中...")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14pt;
                    font-weight: bold;
                    color: #059669;
                    padding: 10px;
                    background: #ecfdf5;
                    border-radius: 8px;
                    margin: 10px 0;
                }
            """)
            
            print(f"✅ 开始监控设备，阈值: {threshold_db}dB")
        else:
            QMessageBox.warning(self, "错误", "启动监控失败")
    
    def stop_monitoring(self):
        """停止监控"""
        self.integration.stop_monitoring_all()
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.device_combo.setEnabled(True)
        self.threshold_spin.setEnabled(True)
        
        self.status_label.setText("状态: 已停止")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #6b7280;
                padding: 10px;
                background: #f3f4f6;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        
        self.level_label.setText("音频电平: -60.0 dB")
        print("⏹️ 停止监控")
    
    @pyqtSlot(str, float)
    def on_audio_level_changed(self, source_name, db_level):
        """音频电平变化"""
        self.audio_levels[source_name] = db_level
    
    @pyqtSlot(str, str)
    def on_device_error(self, source_name, error_msg):
        """设备错误"""
        QMessageBox.warning(self, "设备错误", f"音频源 '{source_name}' 发生错误：\n{error_msg}")
    
    @pyqtSlot(str, bool)
    def on_speaking_state_changed(self, source_name, is_speaking):
        """说话状态变化"""
        if is_speaking:
            self.status_label.setText("状态: 🎤 检测到声音")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14pt;
                    font-weight: bold;
                    color: #dc2626;
                    padding: 10px;
                    background: #fef2f2;
                    border-radius: 8px;
                    margin: 10px 0;
                }
            """)
            print(f"🎤 {source_name} 开始说话")
        else:
            self.status_label.setText("状态: 监控中...")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14pt;
                    font-weight: bold;
                    color: #059669;
                    padding: 10px;
                    background: #ecfdf5;
                    border-radius: 8px;
                    margin: 10px 0;
                }
            """)
            print(f"🔇 {source_name} 停止说话")
    
    def update_display(self):
        """更新显示"""
        if "demo_source" in self.audio_levels:
            db_level = self.audio_levels["demo_source"]
            self.level_label.setText(f"音频电平: {db_level:.1f} dB")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.integration.cleanup()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
        }
        QComboBox, QSpinBox, QPushButton {
            padding: 8px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            background: white;
        }
        QComboBox:focus, QSpinBox:focus {
            border-color: #3b82f6;
        }
        QPushButton {
            background: #3b82f6;
            color: white;
            font-weight: bold;
        }
        QPushButton:hover {
            background: #2563eb;
        }
        QPushButton:disabled {
            background: #9ca3af;
            color: #6b7280;
        }
    """)
    
    # 创建演示窗口
    demo = AudioMonitoringDemo()
    demo.show()
    
    print("🎵 本地音频监控演示启动")
    print("请选择音频设备并开始监控")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
