#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地音频监控模块
用于监听本地音频设备的实时音频电平
"""

import sys
import time
import threading
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QMessageBox

class LocalAudioMonitor(QObject):
    """本地音频监控器"""
    
    # 信号定义
    audio_level_changed = pyqtSignal(str, float)  # 音频源名称, 音频电平(dB)
    device_error = pyqtSignal(str, str)  # 设备名称, 错误信息
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 尝试导入pyaudio
        try:
            import pyaudio
            self.pyaudio = pyaudio
            self.pa = pyaudio.PyAudio()
            self.pyaudio_available = True
            print("✅ PyAudio 初始化成功")
        except ImportError:
            self.pyaudio = None
            self.pa = None
            self.pyaudio_available = False
            print("❌ PyAudio 未安装，本地音频监控功能不可用")
        except Exception as e:
            self.pyaudio = None
            self.pa = None
            self.pyaudio_available = False
            print(f"❌ PyAudio 初始化失败: {e}")
        
        # 监控状态
        self.monitoring_sources = {}  # 存储正在监控的音频源
        self.monitoring_threads = {}  # 存储监控线程
        self.is_monitoring = False
        
        # 音频参数
        self.sample_rate = 44100
        self.chunk_size = 1024
        self.channels = 1
        self.format = self.pyaudio.paInt16 if self.pyaudio_available else None
        
    def get_audio_devices(self):
        """获取可用的音频设备列表"""
        if not self.pyaudio_available:
            return []
        
        devices = []
        try:
            device_count = self.pa.get_device_count()
            for i in range(device_count):
                try:
                    device_info = self.pa.get_device_info_by_index(i)
                    # 只返回输入设备
                    if device_info['maxInputChannels'] > 0:
                        devices.append({
                            'index': i,
                            'name': device_info['name'],
                            'channels': device_info['maxInputChannels'],
                            'sample_rate': int(device_info['defaultSampleRate'])
                        })
                except Exception as e:
                    print(f"获取设备 {i} 信息失败: {e}")
                    continue
        except Exception as e:
            print(f"获取音频设备列表失败: {e}")
        
        return devices
    
    def start_monitoring(self, source_name, device_index, threshold_db=-30.0):
        """开始监控指定的音频设备
        
        Args:
            source_name: 音频源名称（用于标识）
            device_index: 音频设备索引
            threshold_db: 触发阈值（dB）
        """
        if not self.pyaudio_available:
            print(f"❌ PyAudio不可用，无法监控 {source_name}")
            return False
        
        if source_name in self.monitoring_sources:
            print(f"⚠️ {source_name} 已在监控中")
            return True
        
        try:
            # 验证设备是否可用
            device_info = self.pa.get_device_info_by_index(device_index)
            if device_info['maxInputChannels'] == 0:
                print(f"❌ 设备 {device_info['name']} 不支持音频输入")
                return False
            
            # 创建音频流
            stream = self.pa.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.chunk_size
            )
            
            # 保存监控配置
            self.monitoring_sources[source_name] = {
                'device_index': device_index,
                'device_name': device_info['name'],
                'threshold_db': threshold_db,
                'stream': stream,
                'is_speaking': False,
                'last_trigger_time': 0.0
            }
            
            # 启动监控线程
            thread = threading.Thread(
                target=self._monitor_audio_thread,
                args=(source_name,),
                daemon=True
            )
            thread.start()
            self.monitoring_threads[source_name] = thread
            
            print(f"✅ 开始监控 {source_name} -> {device_info['name']}")
            return True
            
        except Exception as e:
            print(f"❌ 启动 {source_name} 监控失败: {e}")
            self.device_error.emit(source_name, str(e))
            return False
    
    def stop_monitoring(self, source_name):
        """停止监控指定的音频源"""
        if source_name not in self.monitoring_sources:
            return
        
        try:
            # 关闭音频流
            source_config = self.monitoring_sources[source_name]
            if 'stream' in source_config:
                source_config['stream'].stop_stream()
                source_config['stream'].close()
            
            # 移除配置
            del self.monitoring_sources[source_name]
            if source_name in self.monitoring_threads:
                del self.monitoring_threads[source_name]
            
            print(f"✅ 停止监控 {source_name}")
            
        except Exception as e:
            print(f"❌ 停止 {source_name} 监控失败: {e}")
    
    def stop_all_monitoring(self):
        """停止所有音频监控"""
        source_names = list(self.monitoring_sources.keys())
        for source_name in source_names:
            self.stop_monitoring(source_name)
        
        print("✅ 已停止所有音频监控")
    
    def _monitor_audio_thread(self, source_name):
        """音频监控线程"""
        source_config = self.monitoring_sources.get(source_name)
        if not source_config:
            return
        
        stream = source_config['stream']
        threshold_db = source_config['threshold_db']
        
        print(f"🎵 {source_name} 监控线程启动")
        
        try:
            while source_name in self.monitoring_sources:
                try:
                    # 读取音频数据
                    data = stream.read(self.chunk_size, exception_on_overflow=False)
                    
                    # 转换为numpy数组
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    
                    # 计算RMS音量
                    rms = np.sqrt(np.mean(audio_data.astype(np.float64) ** 2))
                    
                    # 转换为dB
                    if rms > 0:
                        db_level = 20 * np.log10(rms / 32767.0)  # 16位音频的最大值
                    else:
                        db_level = -60.0  # 静音
                    
                    # 发送音频电平信号
                    self.audio_level_changed.emit(source_name, db_level)
                    
                    # 短暂休眠，避免CPU占用过高
                    time.sleep(0.01)  # 10ms
                    
                except Exception as e:
                    print(f"❌ {source_name} 音频读取错误: {e}")
                    time.sleep(0.1)
                    
        except Exception as e:
            print(f"❌ {source_name} 监控线程异常: {e}")
        finally:
            print(f"🎵 {source_name} 监控线程结束")
    
    def update_threshold(self, source_name, threshold_db):
        """更新指定音频源的阈值"""
        if source_name in self.monitoring_sources:
            self.monitoring_sources[source_name]['threshold_db'] = threshold_db
            print(f"✅ 更新 {source_name} 阈值为 {threshold_db:.1f}dB")
    
    def get_monitoring_status(self):
        """获取当前监控状态"""
        status = {}
        for source_name, config in self.monitoring_sources.items():
            status[source_name] = {
                'device_name': config['device_name'],
                'threshold_db': config['threshold_db'],
                'is_speaking': config.get('is_speaking', False)
            }
        return status
    
    def cleanup(self):
        """清理资源"""
        self.stop_all_monitoring()
        if self.pa:
            try:
                self.pa.terminate()
                print("✅ PyAudio 资源已清理")
            except Exception as e:
                print(f"❌ 清理PyAudio资源失败: {e}")


class AudioIntegrationManager(QObject):
    """音频集成管理器 - 连接本地监控和OBS控制"""
    
    # 信号定义
    speaking_state_changed = pyqtSignal(str, bool)  # 音频源名称, 是否在说话
    
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        
        self.main_window = main_window
        self.local_monitor = LocalAudioMonitor(self)
        
        # 连接信号
        self.local_monitor.audio_level_changed.connect(self._on_audio_level_changed)
        self.local_monitor.device_error.connect(self._on_device_error)
        
        # 音频源映射 (本地设备 -> OBS源名)
        self.source_mappings = {}
        
        # 说话状态管理
        self.speaking_states = {}
        self.hold_timers = {}  # 保持时间定时器
        
    def add_source_mapping(self, local_name, obs_name, device_index, threshold_db=-30.0, hold_time=0.5):
        """添加音频源映射
        
        Args:
            local_name: 本地音频源名称
            obs_name: OBS中的音频源名称
            device_index: 本地音频设备索引
            threshold_db: 触发阈值
            hold_time: 保持时间（秒）
        """
        self.source_mappings[local_name] = {
            'obs_name': obs_name,
            'device_index': device_index,
            'threshold_db': threshold_db,
            'hold_time': hold_time
        }
        
        # 初始化说话状态
        self.speaking_states[local_name] = False
        
        print(f"✅ 添加音频源映射: {local_name} -> {obs_name}")
    
    def start_monitoring_all(self):
        """开始监控所有配置的音频源"""
        if not self.local_monitor.pyaudio_available:
            QMessageBox.warning(
                self.main_window,
                "警告",
                "PyAudio未安装或不可用，无法启动本地音频监控。\n\n"
                "请安装PyAudio：pip install pyaudio"
            )
            return False
        
        success_count = 0
        for local_name, mapping in self.source_mappings.items():
            if self.local_monitor.start_monitoring(
                local_name,
                mapping['device_index'],
                mapping['threshold_db']
            ):
                success_count += 1
        
        print(f"✅ 成功启动 {success_count}/{len(self.source_mappings)} 个音频源监控")
        return success_count > 0
    
    def stop_monitoring_all(self):
        """停止所有音频监控"""
        self.local_monitor.stop_all_monitoring()
        
        # 清理保持时间定时器
        for timer in self.hold_timers.values():
            if timer.isActive():
                timer.stop()
        self.hold_timers.clear()
        
        # 重置说话状态
        for local_name in self.speaking_states:
            self.speaking_states[local_name] = False
        
        print("✅ 已停止所有音频监控")
    
    def _on_audio_level_changed(self, source_name, db_level):
        """处理音频电平变化"""
        if source_name not in self.source_mappings:
            return
        
        mapping = self.source_mappings[source_name]
        threshold_db = mapping['threshold_db']
        hold_time = mapping['hold_time']
        
        # 判断是否超过阈值
        is_above_threshold = db_level > threshold_db
        current_speaking = self.speaking_states.get(source_name, False)
        
        if is_above_threshold and not current_speaking:
            # 开始说话
            self.speaking_states[source_name] = True
            self.speaking_state_changed.emit(source_name, True)
            
            # 取消之前的保持定时器
            if source_name in self.hold_timers:
                self.hold_timers[source_name].stop()
            
            print(f"🎯 {source_name} 开始说话 ({db_level:.1f}dB)")
            
        elif not is_above_threshold and current_speaking:
            # 可能停止说话，启动保持定时器
            if source_name not in self.hold_timers:
                self.hold_timers[source_name] = QTimer()
                self.hold_timers[source_name].setSingleShot(True)
                self.hold_timers[source_name].timeout.connect(
                    lambda name=source_name: self._on_hold_timeout(name)
                )
            
            self.hold_timers[source_name].start(int(hold_time * 1000))  # 转换为毫秒
    
    def _on_hold_timeout(self, source_name):
        """保持时间超时，确认停止说话"""
        if self.speaking_states.get(source_name, False):
            self.speaking_states[source_name] = False
            self.speaking_state_changed.emit(source_name, False)
            print(f"🎯 {source_name} 停止说话")
    
    def _on_device_error(self, source_name, error_msg):
        """处理设备错误"""
        print(f"❌ {source_name} 设备错误: {error_msg}")
        QMessageBox.warning(
            self.main_window,
            "音频设备错误",
            f"音频源 '{source_name}' 发生错误：\n{error_msg}"
        )
    
    def get_available_devices(self):
        """获取可用的音频设备"""
        return self.local_monitor.get_audio_devices()
    
    def cleanup(self):
        """清理资源"""
        self.stop_monitoring_all()
        self.local_monitor.cleanup()
