#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频依赖安装脚本
自动安装本地音频监控所需的依赖库
"""

import sys
import subprocess
import os
import platform

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def check_pip():
    """检查pip是否可用"""
    print("\n🔍 检查pip...")
    try:
        import pip
        print("✅ pip可用")
        return True
    except ImportError:
        print("❌ pip不可用")
        return False

def install_pyaudio():
    """安装PyAudio"""
    print("\n📦 安装PyAudio...")
    
    # 检查是否已安装
    try:
        import pyaudio
        print("✅ PyAudio已安装")
        return True
    except ImportError:
        pass
    
    # 根据操作系统选择安装方法
    system = platform.system().lower()
    
    if system == "windows":
        # Windows系统，尝试多种安装方法
        print("🪟 检测到Windows系统")
        
        # 方法1: 直接pip安装
        if run_command("pip install pyaudio", "pip安装PyAudio"):
            return True
        
        # 方法2: 使用预编译的wheel
        print("\n🔄 尝试使用预编译wheel...")
        architecture = platform.architecture()[0]
        python_version = f"{sys.version_info.major}{sys.version_info.minor}"
        
        wheel_urls = [
            f"https://download.lfd.uci.edu/pythonlibs/archived/PyAudio-0.2.11-cp{python_version}-cp{python_version}-win_amd64.whl",
            f"https://download.lfd.uci.edu/pythonlibs/archived/PyAudio-0.2.11-cp{python_version}-cp{python_version}-win32.whl"
        ]
        
        for url in wheel_urls:
            if run_command(f"pip install {url}", f"安装预编译wheel"):
                return True
        
        # 方法3: 提示手动安装
        print("\n⚠️ 自动安装失败，请尝试手动安装:")
        print("1. 访问 https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio")
        print("2. 下载适合您Python版本的PyAudio wheel文件")
        print("3. 使用 pip install 下载的文件名.whl 安装")
        return False
        
    elif system == "linux":
        print("🐧 检测到Linux系统")
        
        # 先安装系统依赖
        print("安装系统依赖...")
        distro_commands = [
            "sudo apt-get update && sudo apt-get install -y portaudio19-dev python3-pyaudio",  # Ubuntu/Debian
            "sudo yum install -y portaudio-devel",  # CentOS/RHEL
            "sudo dnf install -y portaudio-devel",  # Fedora
        ]
        
        for cmd in distro_commands:
            if run_command(cmd, "安装系统依赖"):
                break
        
        # 然后安装Python包
        return run_command("pip install pyaudio", "pip安装PyAudio")
        
    elif system == "darwin":
        print("🍎 检测到macOS系统")
        
        # 使用Homebrew安装依赖
        if run_command("brew install portaudio", "安装portaudio"):
            return run_command("pip install pyaudio", "pip安装PyAudio")
        else:
            print("⚠️ 请先安装Homebrew: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            return False
    
    else:
        print(f"❌ 不支持的操作系统: {system}")
        return False

def install_other_deps():
    """安装其他依赖"""
    deps = [
        ("numpy", "数值计算库"),
        ("sounddevice", "音频设备接口库（备用）"),
    ]
    
    success_count = 0
    for package, description in deps:
        print(f"\n📦 安装{description}...")
        
        # 检查是否已安装
        try:
            __import__(package)
            print(f"✅ {package}已安装")
            success_count += 1
            continue
        except ImportError:
            pass
        
        if run_command(f"pip install {package}", f"安装{package}"):
            success_count += 1
    
    return success_count == len(deps)

def test_installation():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    try:
        import pyaudio
        import numpy as np
        
        # 创建PyAudio实例
        pa = pyaudio.PyAudio()
        device_count = pa.get_device_count()
        pa.terminate()
        
        print(f"✅ PyAudio测试成功，检测到 {device_count} 个音频设备")
        print("✅ NumPy测试成功")
        print("✅ 所有依赖安装成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎵 音频依赖安装脚本")
    print("=" * 50)
    
    # 检查基础环境
    if not check_python_version():
        return False
    
    if not check_pip():
        return False
    
    # 安装依赖
    success = True
    
    if not install_pyaudio():
        print("\n❌ PyAudio安装失败")
        success = False
    
    if not install_other_deps():
        print("\n❌ 其他依赖安装失败")
        success = False
    
    # 测试安装
    if success:
        if test_installation():
            print("\n🎉 所有依赖安装完成！")
            print("\n📝 使用说明:")
            print("1. 现在可以使用本地音频监控功能")
            print("2. 在程序中启用三重音频闪避")
            print("3. 选择要监控的音频设备")
            print("4. 配置触发阈值和保持时间")
            return True
        else:
            success = False
    
    if not success:
        print("\n❌ 安装过程中出现错误")
        print("\n🔧 故障排除:")
        print("1. 确保以管理员权限运行此脚本")
        print("2. 检查网络连接")
        print("3. 尝试手动安装: pip install pyaudio numpy")
        print("4. 查看上面的错误信息进行针对性解决")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ 用户取消安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装脚本异常: {e}")
        input("\n按回车键退出...")
        sys.exit(1)
