@echo off
chcp 65001 >nul
title OBS去重软件 - 支持本地音频监控

echo.
echo 🎬 OBS去重软件 v1.3 - 支持本地音频监控
echo ==========================================
echo.

REM 检查虚拟环境是否存在
if not exist ".venv\Scripts\python.exe" (
    echo ❌ 虚拟环境不存在，请先创建虚拟环境
    echo.
    echo 创建虚拟环境命令：
    echo python -m venv .venv
    echo.
    pause
    exit /b 1
)

echo ✅ 找到虚拟环境
echo.

REM 检查音频监控依赖
echo 🔍 检查音频监控依赖...
.venv\Scripts\python.exe -c "import pyaudio, numpy; print('✅ 音频监控依赖可用')" 2>nul
if errorlevel 1 (
    echo ⚠️  音频监控依赖未安装，本地音频监控功能将不可用
    echo.
    echo 如需使用本地音频监控功能，请运行：
    echo .venv\Scripts\python.exe install_audio_deps.py
    echo.
    echo 按任意键继续启动程序（将使用OBS WebSocket监控）...
    pause >nul
) else (
    echo ✅ 本地音频监控功能可用
)

echo.
echo 🚀 启动OBS去重软件...
echo.
echo 💡 使用提示：
echo 1. 确保OBS Studio正在运行
echo 2. 在音频去重页面可以选择"使用本地音频监控"
echo 3. 本地监控提供更快速、更精确的音频闪避
echo.

.venv\Scripts\python.exe main_module.py

echo.
echo 程序已退出
pause
